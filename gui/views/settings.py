"""
Settings view for the application with separate buttons for username and password changes.
"""
import flet as ft
from gui.components.layout import create_page_layout
from gui.config.language import get_text, LANGUAGE_NAMES, DEFAULT_LANGUAGE

def create_settings_view(page: ft.Page):
    """
    Create the settings view with separate sections for username and password changes.

    Args:
        page: The Flet page object

    Returns:
        ft.View: The settings view
    """
    # Get current language
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    # Store the previous route to return to after saving
    previous_route = getattr(page, '_previous_route', '/dashboard')
    if hasattr(page, 'views') and len(page.views) > 1:
        # Get the route from the previous view if available
        for view in reversed(page.views[:-1]):  # Skip current view
            if view.route != '/settings':
                previous_route = view.route
                break

    # Create language dropdown with modern styling
    language_dropdown = ft.Dropdown(
        label=get_text("select_language", current_language),
        value=current_language,
        options=[
            ft.dropdown.Option(key=lang_code, text=name)
            for lang_code, name in LANGUAGE_NAMES.items()
        ],
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.BLUE_200,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
    )

    # Current credentials display
    current_username_display = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.PERSON, color=ft.colors.BLUE_600, size=20),
            ft.Text(
                f"{get_text('username', current_language)}: {page.app_state.username}",
                size=14,
                weight=ft.FontWeight.W_500,
                color=ft.colors.GREY_700
            )
        ], spacing=8),
        padding=ft.padding.all(12),
        bgcolor=ft.colors.BLUE_50,
        border_radius=8,
        border=ft.border.all(1, ft.colors.BLUE_200),
    )

    # Teacher profile fields
    first_name_field = ft.TextField(
        label=get_text("first_name", current_language),
        value=page.app_state.first_name,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("enter_first_name", current_language),
        prefix_icon=ft.icons.PERSON,
    )

    last_name_field = ft.TextField(
        label=get_text("last_name", current_language),
        value=page.app_state.last_name,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("enter_last_name", current_language),
        prefix_icon=ft.icons.PERSON,
    )

    # Username change fields
    current_password_username_field = ft.TextField(
        label=get_text("current_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("current_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    new_username_field = ft.TextField(
        label=get_text("new_username", current_language),
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("new_username", current_language),
        prefix_icon=ft.icons.PERSON_OUTLINE,
    )

    # Password change fields
    current_password_password_field = ft.TextField(
        label=get_text("current_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("current_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    new_password_field = ft.TextField(
        label=get_text("new_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("new_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    confirm_password_field = ft.TextField(
        label=get_text("confirm_password", current_language),
        password=True,
        can_reveal_password=True,
        width=300,
        border_radius=12,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREY_300,
        focused_border_color=ft.colors.BLUE_600,
        text_style=ft.TextStyle(size=14),
        label_style=ft.TextStyle(size=12, color=ft.colors.GREY_600),
        hint_text=get_text("confirm_password", current_language),
        prefix_icon=ft.icons.LOCK_OUTLINE,
    )

    # Success message containers
    profile_success_message = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN_600, size=20),
            ft.Text(
                get_text("profile_updated", current_language),
                color=ft.colors.GREEN_600,
                weight=ft.FontWeight.W_500
            )
        ], spacing=8),
        padding=ft.padding.all(12),
        bgcolor=ft.colors.GREEN_50,
        border_radius=8,
        border=ft.border.all(1, ft.colors.GREEN_200),
        visible=False
    )

    username_success_message = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN_600, size=20),
            ft.Text(
                get_text("username_updated", current_language),
                color=ft.colors.GREEN_600,
                weight=ft.FontWeight.W_500
            )
        ], spacing=8),
        padding=ft.padding.all(12),
        bgcolor=ft.colors.GREEN_50,
        border_radius=8,
        border=ft.border.all(1, ft.colors.GREEN_200),
        visible=False
    )

    password_success_message = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN_600, size=20),
            ft.Text(
                get_text("password_updated", current_language),
                color=ft.colors.GREEN_600,
                weight=ft.FontWeight.W_500
            )
        ], spacing=8),
        padding=ft.padding.all(12),
        bgcolor=ft.colors.GREEN_50,
        border_radius=8,
        border=ft.border.all(1, ft.colors.GREEN_200),
        visible=False
    )

    # Save profile function
    def save_profile(_):
        # Reset error states and hide success message
        first_name_field.error_text = None
        last_name_field.error_text = None
        profile_success_message.visible = False

        try:
            page.app_state.update_profile(
                first_name=first_name_field.value,
                last_name=last_name_field.value
            )

            # Show success message
            profile_success_message.visible = True
            page.show_snack_bar(
                ft.SnackBar(
                    content=ft.Text(get_text("profile_updated", current_language)),
                    bgcolor=ft.colors.GREEN_600
                )
            )
        except Exception as e:
            first_name_field.error_text = f"Profile update error: {str(e)}"

        page.update()

    # Save username function
    def save_username(_):
        # Reset error states and hide success message
        current_password_username_field.error_text = None
        new_username_field.error_text = None
        username_success_message.visible = False
        has_error = False

        # Validate current password
        if not current_password_username_field.value:
            current_password_username_field.error_text = get_text("password_required", current_language)
            has_error = True
        elif not page.app_state.verify_password(current_password_username_field.value):
            current_password_username_field.error_text = get_text("invalid_current_password", current_language)
            has_error = True

        # Validate new username
        if not new_username_field.value:
            new_username_field.error_text = get_text("username_required", current_language)
            has_error = True
        elif len(new_username_field.value) < 3:
            new_username_field.error_text = get_text("username_too_short", current_language)
            has_error = True

        # If all validations pass, update username
        if not has_error:
            try:
                success = page.app_state.update_credentials(new_username=new_username_field.value)
                if success:
                    # Clear fields after successful update
                    current_password_username_field.value = ""
                    new_username_field.value = ""

                    # Update current username display
                    current_username_display.content.controls[1].value = f"{get_text('username', current_language)}: {page.app_state.username}"

                    # Show success message
                    username_success_message.visible = True
                    page.show_snack_bar(
                        ft.SnackBar(
                            content=ft.Text(get_text("username_updated", current_language)),
                            bgcolor=ft.colors.GREEN_600
                        )
                    )
                else:
                    current_password_username_field.error_text = "Failed to update username"
            except Exception as e:
                current_password_username_field.error_text = f"Database error: {str(e)}"

        page.update()

    # Save password function
    def save_password(_):
        # Reset error states and hide success message
        current_password_password_field.error_text = None
        new_password_field.error_text = None
        confirm_password_field.error_text = None
        password_success_message.visible = False
        has_error = False

        # Validate current password
        if not current_password_password_field.value:
            current_password_password_field.error_text = get_text("password_required", current_language)
            has_error = True
        elif not page.app_state.verify_password(current_password_password_field.value):
            current_password_password_field.error_text = get_text("invalid_current_password", current_language)
            has_error = True

        # Validate new password
        if not new_password_field.value:
            new_password_field.error_text = get_text("password_required", current_language)
            has_error = True
        elif len(new_password_field.value) < 6:
            new_password_field.error_text = get_text("password_too_short", current_language)
            has_error = True
        elif new_password_field.value != confirm_password_field.value:
            confirm_password_field.error_text = get_text("passwords_dont_match", current_language)
            has_error = True

        # If all validations pass, update password
        if not has_error:
            try:
                success = page.app_state.update_credentials(new_password=new_password_field.value)
                if success:
                    # Clear fields after successful update
                    current_password_password_field.value = ""
                    new_password_field.value = ""
                    confirm_password_field.value = ""

                    # Show success message
                    password_success_message.visible = True
                    page.show_snack_bar(
                        ft.SnackBar(
                            content=ft.Text(get_text("password_updated", current_language)),
                            bgcolor=ft.colors.GREEN_600
                        )
                    )
                else:
                    current_password_password_field.error_text = "Failed to update password"
            except Exception as e:
                current_password_password_field.error_text = f"Database error: {str(e)}"

        page.update()

    # Save language function
    def save_language(_):
        selected_language = language_dropdown.value
        if selected_language != current_language:
            if hasattr(page, 'app_state'):
                page.app_state.set_language(selected_language)
            else:
                page.language = selected_language
            page.update()
            import time
            time.sleep(0.3)
            page.go(previous_route)

    # Create modern settings content with card-based design - centered
    settings_content = ft.Container(
        content=ft.Column([
            # Header section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.SETTINGS, color=ft.colors.BLUE_600, size=28),
                        ft.Text(
                            get_text("settings", current_language),
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.BLUE_900
                        )
                    ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=8),
                    ft.Text(
                        get_text("settings_description", current_language),
                        size=14,
                        color=ft.colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
            ),

            # Language section with modern card design
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.LANGUAGE, color=ft.colors.BLUE_600, size=20),
                        ft.Text(
                            get_text("language", current_language),
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.colors.GREY_800
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),
                    ft.Container(
                        content=language_dropdown,
                        alignment=ft.alignment.center
                    ),
                    ft.Container(height=16),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.icons.SAVE, size=18),
                            ft.Text(get_text("save", current_language), size=14, weight=ft.FontWeight.W_500)
                        ], spacing=8, tight=True),
                        on_click=save_language,
                        width=200,
                        height=40,
                        style=ft.ButtonStyle(
                            bgcolor=ft.colors.BLUE_600,
                            color=ft.colors.WHITE,
                            shape=ft.RoundedRectangleBorder(radius=12),
                            elevation=2,
                        ),
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
                bgcolor=ft.colors.WHITE,
                border_radius=16,
                width=500,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=10,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                ),
            ),

            # Current credentials display section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.ACCOUNT_CIRCLE, color=ft.colors.BLUE_600, size=20),
                        ft.Text(
                            get_text("current_account", current_language),
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.colors.GREY_800
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),
                    ft.Container(
                        content=current_username_display,
                        alignment=ft.alignment.center
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
                bgcolor=ft.colors.WHITE,
                border_radius=16,
                width=500,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=10,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                ),
            ),

            # Teacher profile section with modern card design
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.PERSON, color=ft.colors.BLUE_600, size=20),
                        ft.Text(
                            get_text("teacher_profile", current_language),
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.colors.GREY_800
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),
                    first_name_field,
                    ft.Container(height=16),
                    last_name_field,
                    ft.Container(height=16),
                    profile_success_message,
                    ft.Container(height=16),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.icons.SAVE, size=18),
                            ft.Text(get_text("save", current_language), size=14, weight=ft.FontWeight.W_500)
                        ], spacing=8, tight=True),
                        on_click=save_profile,
                        width=200,
                        height=40,
                        style=ft.ButtonStyle(
                            bgcolor=ft.colors.GREEN_600,
                            color=ft.colors.WHITE,
                            shape=ft.RoundedRectangleBorder(radius=12),
                            elevation=2,
                        ),
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
                bgcolor=ft.colors.WHITE,
                border_radius=16,
                width=500,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=10,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                ),
            ),

            # Username change section with modern card design
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.PERSON_OUTLINE, color=ft.colors.ORANGE_600, size=20),
                        ft.Text(
                            get_text("change_username", current_language),
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.colors.GREY_800
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),
                    current_password_username_field,
                    ft.Container(height=16),
                    new_username_field,
                    ft.Container(height=16),
                    username_success_message,
                    ft.Container(height=16),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.icons.SAVE, size=18),
                            ft.Text(get_text("save_username", current_language), size=14, weight=ft.FontWeight.W_500)
                        ], spacing=8, tight=True),
                        on_click=save_username,
                        width=200,
                        height=40,
                        style=ft.ButtonStyle(
                            bgcolor=ft.colors.ORANGE_600,
                            color=ft.colors.WHITE,
                            shape=ft.RoundedRectangleBorder(radius=12),
                            elevation=2,
                        ),
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
                bgcolor=ft.colors.WHITE,
                border_radius=16,
                width=500,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=10,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                ),
            ),

            # Password change section with modern card design
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.LOCK_OUTLINE, color=ft.colors.PURPLE_600, size=20),
                        ft.Text(
                            get_text("change_password", current_language),
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.colors.GREY_800
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),
                    current_password_password_field,
                    ft.Container(height=16),
                    new_password_field,
                    ft.Container(height=16),
                    confirm_password_field,
                    ft.Container(height=16),
                    password_success_message,
                    ft.Container(height=16),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.icons.SAVE, size=18),
                            ft.Text(get_text("save_password", current_language), size=14, weight=ft.FontWeight.W_500)
                        ], spacing=8, tight=True),
                        on_click=save_password,
                        width=200,
                        height=40,
                        style=ft.ButtonStyle(
                            bgcolor=ft.colors.PURPLE_600,
                            color=ft.colors.WHITE,
                            shape=ft.RoundedRectangleBorder(radius=12),
                            elevation=2,
                        ),
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
                bgcolor=ft.colors.WHITE,
                border_radius=16,
                width=500,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=10,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                ),
            ),
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0,
        scroll=ft.ScrollMode.AUTO),
        alignment=ft.alignment.center,
        expand=True
    )

    # Create the settings view without title (we have custom header)
    return create_page_layout(
        page,
        "",  # No title since we have custom header
        settings_content
    )