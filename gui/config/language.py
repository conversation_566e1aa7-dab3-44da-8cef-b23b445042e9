"""
Language management for the application.
"""
import flet as ft

# Available languages
LANGUAGE_ENGLISH = "en"
LANGUAGE_ARABIC = "ar"
LANGUAGE_FRENCH = "fr"

# Default language
DEFAULT_LANGUAGE = LANGUAGE_FRENCH

# Language display names
LANGUAGE_NAMES = {
    LANGUAGE_ENGLISH: "English",
    LANGUAGE_ARABIC: "العربية",
    LANGUAGE_FRENCH: "Français"
}

# Translations dictionary
# Structure: {language_code: {key: translation}}
TRANSLATIONS = {
    LANGUAGE_ENGLISH: {
        # App general
        "app_name": "Teacher Assistant",
        "dashboard": "Dashboard",
        "classes": "Classes",
        "students": "Students",
        "subjects": "Subjects",
        "quizzes": "Quizzes",
        "attendance": "Attendance",
        "settings": "Settings",

        # Authentication
        "login": "Login",
        "username": "Username",
        "password": "Password",
        "username_required": "Username is required",
        "password_required": "Password is required",
        "invalid_username": "Invalid username",
        "invalid_password": "Invalid password",
        "please_login_to_continue": "Please login to continue",

        # Settings
        "language": "Language",
        "select_language": "Select Language",
        "theme": "Theme",
        "light_mode": "Light Mode",
        "dark_mode": "Dark Mode",
        "change_credentials": "Change Credentials",
        "change_username": "Change Username",
        "change_password": "Change Password",
        "current_password": "Current Password",
        "new_username": "New Username",
        "new_password": "New Password",
        "confirm_password": "Confirm Password",
        "invalid_current_password": "Invalid current password",
        "username_required": "Username is required",
        "password_required": "Password is required",
        "username_too_short": "Username must be at least 3 characters",
        "password_too_short": "Password must be at least 6 characters",
        "passwords_dont_match": "Passwords don't match",
        "credentials_updated": "Credentials updated successfully",
        "username_updated": "Username updated successfully",
        "password_updated": "Password updated successfully",
        "save": "Save",
        "save_username": "Save Username",
        "save_password": "Save Password",
        "cancel": "Cancel",
        "reset": "Reset",
        "settings_saved": "Settings saved successfully",
        "settings_description": "Manage your preferences and account settings",
        "current_account": "Current Account",

        # Dashboard
        "view_details": "View Details",

        # Common actions
        "add": "Add",
        "edit": "Edit",
        "delete": "Delete",
        "close": "Close",
        "back": "Back",
        "search": "Search",
        "no_data": "No Data",

        # Students page
        "student_management": "Student Management",
        "enrolled_students": "Enrolled Students",
        "filter_by_class": "Filter by Class",
        "select_class": "Select a class",
        "add_new_student": "Add New Student",
        "add_student": "Add Student",
        "showing_all": "Showing all",
        "showing": "Showing",
        "found": "Found",
        "students_matching": "student(s) matching",
        "in": "in",
        "clear_search": "Clear search",
        "student": "Student",
        "deleted": "deleted",
        "failed_to_delete_student": "Failed to delete student",
        "delete_student": "Delete Student",
        "delete_student_confirmation": "Are you sure you want to delete this student? This action cannot be undone.",
        "student_name": "Student Name",
        "enter_student_name": "Enter student name",
        "please_enter_student_name": "Please enter a student name",
        "face_enrollment_future": "Face enrollment functionality will be implemented in a future update.",
        "would_be_added": "would be added here",
        "enter_student_name_prompt": "Enter the student's name:",
        "face_enrollment_note": "Note: In a real implementation, this would capture face images for enrollment.",
        "update_face_encoding_for": "Update face encoding for",
        "upload_clear_face_image": "Please upload at least one clear image of the student's face",
        "captured_images": "Captured Images",
        "update_face_encoding": "Update Face Encoding",
        "update": "Update",
        "upload_image": "Upload Image",
        "max_images_allowed": "Maximum of 5 images allowed. Please remove some images first.",
        "only_add": "You can only add",
        "more_images": "more image(s). Please select fewer images.",
        "error_processing_image": "Error processing image",
        "no_face_detected": "No face detected in the image. Please try again.",
        "multiple_faces_detected": "Multiple faces detected in the image. Please use an image with only one face.",
        "please_upload_image": "Please upload at least one image",
        "processing": "Processing",
        "updating_face_encoding": "Updating face encoding...",
        "face_encoding_for": "Face encoding for",
        "updated_successfully": "updated successfully",
        "failed_to_update": "Failed to update face encoding for",
        "error_updating": "Error updating face encoding",

        # Classes page
        "class_management": "Class Management",
        "class_name": "Class Name",
        "enter_class_name": "Enter new class name",
        "description_optional": "Description (Optional)",
        "enter_class_description": "Enter class description",
        "please_enter_class_name": "Please enter a class name",
        "failed_to_create_class": "Failed to create class",
        "add_class": "Add Class",
        "add_new_class": "Add New Class",
        "no_classes_yet": "No classes yet",
        "create_first_class": "Create your first class using the form above",
        "existing_classes": "Existing Classes",
        "view_students": "View Students",
        "rename_class": "Rename Class",
        "delete_class": "Delete Class",
        "assign_students": "Assign Students",
        "take_attendance": "Take Attendance",
        "qr_enrollment": "QR Enrollment",
        "student": "student",
        "students": "students",

        # Subjects page
        "subject_management": "Subject Management",
        "subject_name": "Subject Name",
        "enter_subject_name": "Enter subject name",
        "teacher_name": "Teacher Name",
        "teacher_name_optional": "Teacher Name (Optional)",
        "enter_teacher_name": "Enter teacher name",
        "please_select_class": "Please select a class",
        "please_enter_subject_name": "Please enter a subject name",
        "subject": "Subject",
        "created_successfully": "created successfully",
        "failed_to_create_subject": "Failed to create subject",
        "add_subject": "Add Subject",
        "add_new_subject": "Add New Subject",
        "existing_subjects": "Existing Subjects",
        "edit_subject": "Edit Subject",
        "edit_subject_details": "Edit subject details:",
        "subject_updated_successfully": "Subject updated successfully",
        "failed_to_update_subject": "Failed to update subject",
        "delete_subject": "Delete Subject",
        "delete_subject_confirmation": "Are you sure you want to delete this subject? This action cannot be undone.",
        "failed_to_delete_subject": "Failed to delete subject",
        "choose_class": "Choose a class",
        "select_class": "Select Class",

        # Quizzes page
        "quiz_management": "Quiz Management",
        "quiz_title": "Quiz Title",
        "enter_quiz_title": "Enter quiz title",
        "quiz_description": "Quiz Description",
        "enter_quiz_description": "Enter quiz description",
        "select_class": "Select Class",
        "select_subject": "Select Subject",
        "add_quiz": "Add Quiz",
        "create_quiz": "Create Quiz",
        "no_quizzes_yet": "No quizzes yet",
        "create_first_quiz": "Create your first quiz using the form above",
        "existing_quizzes": "Existing Quizzes",
        "view_quiz_details": "View Quiz Details",
        "edit_quiz": "Edit Quiz",
        "delete_quiz": "Delete Quiz",
        "question_text": "Question Text",
        "enter_question": "Enter question",
        "add_question": "Add Question",
        "option_text": "Option Text",
        "enter_option": "Enter option",
        "is_correct": "Is Correct Answer",
        "add_option": "Add Option",
        "save_quiz": "Save Quiz",
        "cancel_quiz": "Cancel",
        "please_enter_quiz_title": "Please enter a quiz title",
        "failed_to_create_quiz": "Failed to create quiz",

        # Attendance page
        "attendance_management": "Attendance Management",
        "attendance_history": "Attendance History",
        "filter_attendance": "Filter Attendance",
        "date_range": "Date Range",
        "from_date": "From Date",
        "to_date": "To Date",
        "select_date": "Select Date",
        "all_classes": "All Classes",
        "all_subjects": "All Subjects",
        "all_students": "All Students",
        "present": "Present",
        "absent": "Absent",
        "status": "Status",
        "date": "Date",
        "time": "Time",
        "total_records": "Total Records",
        "attendance_rate": "Attendance Rate",
        "export_data": "Export Data",
        "no_attendance_records": "No attendance records found",
        "attendance_statistics": "Attendance Statistics",
        "total_days": "Total Days",
        "clear_filters": "Clear Filters",
        "apply_filters": "Apply Filters",

        # Start page
        "ministry_education_tunisia": "Ministry of Education Tunisia",
        "educational_management_system": "Educational management system for teachers",
        "get_started": "Get Started",

        # Navigation
        "overview": "Overview",
        "overview_stats": "Overview & Stats",
        "classes_count": "classes",
        "students_count": "students",
        "subjects_available": "subjects available",
        "quizzes_created": "quizzes created",

        # Tooltips
        "menu": "Menu",
        "settings_tooltip": "Settings",

        # Error messages and dialogs
        "database_error": "Database Error",
        "database_error_message": "Database error. Please restart the application.",
        "retry": "Retry",
        "confirm": "Confirm",
        "submit": "Submit",

        # Dashboard
        "welcome": "Welcome",

        # Classes page
        "search_classes": "Search classes...",
        "classes_available": "classes available",
        "of_classes_found": "of {total} classes found",
        "no_classes_found": "No classes found for '{query}'",
        "try_different_search": "Try a different search term",
        "create_first_class": "Create your first class to get started",
        "enter_class_description": "Enter class description (optional)",
        "add_new_class": "Add New Class",
        "search_students": "Search students...",
        "no_students_found": "No students found for '{query}'",
        "no_students_in_class": "No students found in class '{class_name}'",
        "add_students_to_class": "Add students to this class to see them here",
        "unknown": "Unknown",
        "enrolled": "Enrolled",
        "sessions": "Sessions",
        "attendance_rate": "Attendance Rate",
        "present": "Present",

        # Dialog titles and messages
        "class_students": "Class Students",
        "students_in": "Students in {class_name}",
        "no_students_in_this_class": "No students in this class",
        "avg_attendance": "avg attendance",
        "new_class_name": "New Class Name",
        "enter_new_name_for_class": "Enter a new name for this class:",
        "rename_class": "Rename Class: {class_name}",
        "rename": "Rename",
        "delete_class": "Delete Class: {class_name}",
        "are_you_sure_delete_class": "Are you sure you want to delete this class? \n\nAll students will be unassigned from this class. This action cannot be undone.",
        "assign_students_to": "Assign Students to {class_name}",
        "select_students_to_assign": "Select students to assign to this class:",
        "save": "Save",
        "take_attendance": "Take Attendance: {class_name}",
        "no_subjects_found_for_class": "No subjects found for this class. Please add subjects first.",
        "go_to_subjects": "Go to Subjects",
        "select_subject": "Select Subject",
        "choose_a_subject": "Choose a subject",
        "select_subject_for_attendance": "Select a subject to take attendance for:",
        "get_attendance": "Get Attendance",
        "please_enter_class_name": "Please enter a class name",
        "failed_to_rename_class": "Failed to rename class",
        "failed_to_delete_class": "Failed to delete class",
        "please_select_subject": "Please select a subject",
        "failed_to_start_video_stream": "Failed to start video stream",

        # Subjects page
        "unknown_class": "Unknown Class",
        "your_subjects": "Your Subjects ({count})",
        "edit_subject": "Edit Subject",
        "delete_subject": "Delete Subject",
        "teacher": "Teacher",
        "no_teacher_assigned": "No teacher assigned",
        "no_subjects_found": "No subjects found",
        "no_subjects_match_filters": "No subjects match the selected filters",
        "no_subjects_found_for_query": "No subjects found for '{query}'",
        "search_subjects": "Search subjects...",
        "filter_by_class": "Filter by class",
        "all_classes": "All classes",
        "add_subject": "Add Subject",
        "subject_name": "Subject Name",
        "teacher_name": "Teacher Name",
        "class_assignment": "Class Assignment",
        "select_class": "Select a class",
        "enter_subject_name": "Enter subject name",
        "enter_teacher_name": "Enter teacher name (optional)",
        "please_enter_subject_name": "Please enter a subject name",
        "please_select_class": "Please select a class",
        "failed_to_add_subject": "Failed to add subject",
        "edit_subject_title": "Edit Subject: {subject_name}",
        "failed_to_update_subject": "Failed to update subject",
        "delete_subject_title": "Delete Subject: {subject_name}",
        "are_you_sure_delete_subject": "Are you sure you want to delete this subject?\n\nThis action cannot be undone.",
        "failed_to_delete_subject": "Failed to delete subject",
        "filter_subjects": "Filter Subjects",
        "add_new_subject": "Add New Subject",
        "try_adjusting_filters": "Try adjusting your filters or create a new subject",
        "failed_to_create_subject": "Failed to create subject",
        "edit_subject_details": "Edit the subject details:",
        "subject_updated_successfully": "Subject updated successfully",
        "delete_subject_confirmation": "Are you sure you want to delete this subject?\n\nThis action cannot be undone.",

        # Quizzes page
        "filter_quizzes": "Filter Quizzes",
        "all_subjects": "All Subjects",
        "select_subject": "Select Subject",
        "filter_by_subject": "Filter by Subject",

        # Student details page
        "student_details": "Student Details",
        "back_to_classes": "Back to Classes",
        "face_enrolled": "Face Enrolled",
        "face_not_enrolled": "Face Not Enrolled",
        "class": "Class",
        "completed": "Completed",
        "recent_activity": "Recent Activity",
        "no_recent_activity": "No recent activity",
        "attendance_by_subject": "Attendance by Subject",
        "view_full_history": "View Full History"
    },
    LANGUAGE_ARABIC: {
        # App general
        "app_name": "مساعد المعلم",
        "dashboard": "لوحة التحكم",
        "classes": "الفصول",
        "students": "الطلاب",
        "subjects": "المواد",
        "quizzes": "الاختبارات",
        "attendance": "الحضور",
        "settings": "الإعدادات",

        # Authentication
        "login": "تسجيل الدخول",
        "username": "اسم المستخدم",
        "password": "كلمة المرور",
        "username_required": "اسم المستخدم مطلوب",
        "password_required": "كلمة المرور مطلوبة",
        "invalid_username": "اسم المستخدم غير صحيح",
        "invalid_password": "كلمة المرور غير صحيحة",
        "please_login_to_continue": "يرجى تسجيل الدخول للمتابعة",

        # Settings
        "language": "اللغة",
        "select_language": "اختر اللغة",
        "theme": "المظهر",
        "light_mode": "الوضع الفاتح",
        "dark_mode": "الوضع الداكن",
        "change_credentials": "تغيير بيانات الاعتماد",
        "change_username": "تغيير اسم المستخدم",
        "change_password": "تغيير كلمة المرور",
        "current_password": "كلمة المرور الحالية",
        "new_username": "اسم المستخدم الجديد",
        "new_password": "كلمة المرور الجديدة",
        "confirm_password": "تأكيد كلمة المرور",
        "invalid_current_password": "كلمة المرور الحالية غير صحيحة",
        "username_required": "اسم المستخدم مطلوب",
        "password_required": "كلمة المرور مطلوبة",
        "username_too_short": "يجب أن يكون اسم المستخدم 3 أحرف على الأقل",
        "password_too_short": "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
        "passwords_dont_match": "كلمات المرور غير متطابقة",
        "credentials_updated": "تم تحديث بيانات الاعتماد بنجاح",
        "username_updated": "تم تحديث اسم المستخدم بنجاح",
        "password_updated": "تم تحديث كلمة المرور بنجاح",
        "save": "حفظ",
        "save_username": "حفظ اسم المستخدم",
        "save_password": "حفظ كلمة المرور",
        "cancel": "إلغاء",
        "reset": "إعادة تعيين",
        "settings_saved": "تم حفظ الإعدادات بنجاح",
        "settings_description": "إدارة تفضيلاتك وإعدادات الحساب",
        "current_account": "الحساب الحالي",

        # Dashboard
        "view_details": "عرض التفاصيل",

        # Common actions
        "add": "إضافة",
        "edit": "تعديل",
        "delete": "حذف",
        "close": "إغلاق",
        "back": "رجوع",
        "search": "بحث",
        "no_data": "لا توجد بيانات",

        # Students page
        "student_management": "إدارة الطلاب",
        "enrolled_students": "الطلاب المسجلين",
        "filter_by_class": "تصفية حسب الفصل",
        "select_class": "اختر فصلاً",
        "add_new_student": "إضافة طالب جديد",
        "add_student": "إضافة طالب",
        "showing_all": "عرض كل",
        "showing": "عرض",
        "found": "تم العثور على",
        "students_matching": "طالب (طلاب) مطابق لـ",
        "in": "في",
        "clear_search": "مسح البحث",
        "student": "طالب",
        "deleted": "تم الحذف",
        "failed_to_delete_student": "فشل في حذف الطالب",
        "delete_student": "حذف الطالب",
        "delete_student_confirmation": "هل أنت متأكد من رغبتك في حذف هذا الطالب؟ لا يمكن التراجع عن هذا الإجراء.",
        "student_name": "اسم الطالب",
        "enter_student_name": "أدخل اسم الطالب",
        "please_enter_student_name": "الرجاء إدخال اسم الطالب",
        "face_enrollment_future": "سيتم تنفيذ وظيفة تسجيل الوجه في تحديث مستقبلي.",
        "would_be_added": "سيتم إضافته هنا",
        "enter_student_name_prompt": "أدخل اسم الطالب:",
        "face_enrollment_note": "ملاحظة: في التنفيذ الفعلي، سيتم التقاط صور الوجه للتسجيل.",
        "update_face_encoding_for": "تحديث ترميز الوجه لـ",
        "upload_clear_face_image": "الرجاء تحميل صورة واضحة واحدة على الأقل لوجه الطالب",
        "captured_images": "الصور الملتقطة",
        "update_face_encoding": "تحديث ترميز الوجه",
        "update": "تحديث",
        "upload_image": "تحميل صورة",
        "max_images_allowed": "الحد الأقصى المسموح به هو 5 صور. الرجاء إزالة بعض الصور أولاً.",
        "only_add": "يمكنك إضافة",
        "more_images": "صورة (صور) إضافية فقط. الرجاء تحديد صور أقل.",
        "error_processing_image": "خطأ في معالجة الصورة",
        "no_face_detected": "لم يتم اكتشاف وجه في الصورة. الرجاء المحاولة مرة أخرى.",
        "multiple_faces_detected": "تم اكتشاف عدة وجوه في الصورة. الرجاء استخدام صورة بوجه واحد فقط.",
        "please_upload_image": "الرجاء تحميل صورة واحدة على الأقل",
        "processing": "جاري المعالجة",
        "updating_face_encoding": "جاري تحديث ترميز الوجه...",
        "face_encoding_for": "ترميز الوجه لـ",
        "updated_successfully": "تم التحديث بنجاح",
        "failed_to_update": "فشل في تحديث ترميز الوجه لـ",
        "error_updating": "خطأ في تحديث ترميز الوجه",

        # Classes page
        "class_management": "إدارة الفصول",
        "class_name": "اسم الفصل",
        "enter_class_name": "أدخل اسم الفصل الجديد",
        "description_optional": "الوصف (اختياري)",
        "enter_class_description": "أدخل وصف الفصل",
        "please_enter_class_name": "الرجاء إدخال اسم الفصل",
        "failed_to_create_class": "فشل في إنشاء الفصل",
        "add_class": "إضافة فصل",
        "add_new_class": "إضافة فصل جديد",
        "no_classes_yet": "لا توجد فصول بعد",
        "create_first_class": "قم بإنشاء فصلك الأول باستخدام النموذج أعلاه",
        "existing_classes": "الفصول الموجودة",
        "view_students": "عرض الطلاب",
        "rename_class": "إعادة تسمية الفصل",
        "delete_class": "حذف الفصل",
        "assign_students": "تعيين الطلاب",
        "take_attendance": "تسجيل الحضور",
        "qr_enrollment": "التسجيل برمز QR",
        "student": "طالب",
        "students": "طلاب",

        # Subjects page
        "subject_management": "إدارة المواد",
        "subject_name": "اسم المادة",
        "enter_subject_name": "أدخل اسم المادة",
        "teacher_name": "اسم المعلم",
        "teacher_name_optional": "اسم المعلم (اختياري)",
        "enter_teacher_name": "أدخل اسم المعلم",
        "please_select_class": "الرجاء اختيار فصل",
        "please_enter_subject_name": "الرجاء إدخال اسم المادة",
        "subject": "مادة",
        "created_successfully": "تم إنشاؤها بنجاح",
        "failed_to_create_subject": "فشل في إنشاء المادة",
        "add_subject": "إضافة مادة",
        "add_new_subject": "إضافة مادة جديدة",
        "existing_subjects": "المواد الموجودة",
        "edit_subject": "تعديل المادة",
        "edit_subject_details": "تعديل تفاصيل المادة:",
        "subject_updated_successfully": "تم تحديث المادة بنجاح",
        "failed_to_update_subject": "فشل في تحديث المادة",
        "delete_subject": "حذف المادة",
        "delete_subject_confirmation": "هل أنت متأكد من رغبتك في حذف هذه المادة؟ لا يمكن التراجع عن هذا الإجراء.",
        "failed_to_delete_subject": "فشل في حذف المادة",
        "choose_class": "اختر فصلاً",
        "select_class": "اختر فصلاً",

        # Quizzes page
        "quiz_management": "إدارة الاختبارات",
        "quiz_title": "عنوان الاختبار",
        "enter_quiz_title": "أدخل عنوان الاختبار",
        "quiz_description": "وصف الاختبار",
        "enter_quiz_description": "أدخل وصف الاختبار",
        "select_subject": "اختر المادة",
        "add_quiz": "إضافة اختبار",
        "create_quiz": "إنشاء اختبار",
        "no_quizzes_yet": "لا توجد اختبارات بعد",
        "create_first_quiz": "قم بإنشاء اختبارك الأول باستخدام النموذج أعلاه",
        "existing_quizzes": "الاختبارات الموجودة",
        "view_quiz_details": "عرض تفاصيل الاختبار",
        "edit_quiz": "تعديل الاختبار",
        "delete_quiz": "حذف الاختبار",
        "question_text": "نص السؤال",
        "enter_question": "أدخل السؤال",
        "add_question": "إضافة سؤال",
        "option_text": "نص الخيار",
        "enter_option": "أدخل الخيار",
        "is_correct": "هل هذه الإجابة الصحيحة",
        "add_option": "إضافة خيار",
        "save_quiz": "حفظ الاختبار",
        "cancel_quiz": "إلغاء",
        "please_enter_quiz_title": "الرجاء إدخال عنوان الاختبار",
        "failed_to_create_quiz": "فشل في إنشاء الاختبار",

        # Attendance page
        "attendance_management": "إدارة الحضور",
        "attendance_history": "تاريخ الحضور",
        "filter_attendance": "تصفية الحضور",
        "date_range": "نطاق التاريخ",
        "from_date": "من تاريخ",
        "to_date": "إلى تاريخ",
        "select_date": "اختر التاريخ",
        "all_classes": "جميع الفصول",
        "all_subjects": "جميع المواد",
        "all_students": "جميع الطلاب",
        "present": "حاضر",
        "absent": "غائب",
        "status": "الحالة",
        "date": "التاريخ",
        "time": "الوقت",
        "total_records": "إجمالي السجلات",
        "attendance_rate": "معدل الحضور",
        "export_data": "تصدير البيانات",
        "no_attendance_records": "لم يتم العثور على سجلات حضور",
        "attendance_statistics": "إحصائيات الحضور",
        "total_days": "إجمالي الأيام",
        "clear_filters": "مسح المرشحات",
        "apply_filters": "تطبيق المرشحات",

        # Start page
        "ministry_education_tunisia": "وزارة التربية تونس",
        "educational_management_system": "نظام إدارة تعليمي للمعلمين",
        "get_started": "ابدأ",

        # Navigation
        "overview": "نظرة عامة",
        "overview_stats": "نظرة عامة وإحصائيات",
        "classes_count": "فصول",
        "students_count": "طلاب",
        "subjects_available": "مواد متاحة",
        "quizzes_created": "اختبارات تم إنشاؤها",

        # Tooltips
        "menu": "القائمة",
        "settings_tooltip": "الإعدادات",

        # Error messages and dialogs
        "database_error": "خطأ في قاعدة البيانات",
        "database_error_message": "خطأ في قاعدة البيانات. يرجى إعادة تشغيل التطبيق.",
        "retry": "إعادة المحاولة",
        "confirm": "تأكيد",
        "submit": "إرسال",

        # Dashboard
        "welcome": "أهلاً وسهلاً",

        # Classes page
        "search_classes": "البحث في الفصول...",
        "classes_available": "فصول متاحة",
        "of_classes_found": "من {total} فصول موجودة",
        "no_classes_found": "لم يتم العثور على فصول لـ '{query}'",
        "try_different_search": "جرب مصطلح بحث مختلف",
        "create_first_class": "أنشئ فصلك الأول للبدء",
        "enter_class_description": "أدخل وصف الفصل (اختياري)",
        "add_new_class": "إضافة فصل جديد",
        "search_students": "البحث في الطلاب...",
        "no_students_found": "لم يتم العثور على طلاب لـ '{query}'",
        "no_students_in_class": "لم يتم العثور على طلاب في فصل '{class_name}'",
        "add_students_to_class": "أضف طلاب إلى هذا الفصل لرؤيتهم هنا",
        "unknown": "غير معروف",
        "enrolled": "مسجل",
        "sessions": "الجلسات",
        "attendance_rate": "معدل الحضور",
        "present": "حاضر",

        # Dialog titles and messages
        "class_students": "طلاب الفصل",
        "students_in": "طلاب في {class_name}",
        "no_students_in_this_class": "لا يوجد طلاب في هذا الفصل",
        "avg_attendance": "متوسط الحضور",
        "new_class_name": "اسم الفصل الجديد",
        "enter_new_name_for_class": "أدخل اسماً جديداً لهذا الفصل:",
        "rename_class": "إعادة تسمية الفصل: {class_name}",
        "rename": "إعادة تسمية",
        "delete_class": "حذف الفصل: {class_name}",
        "are_you_sure_delete_class": "هل أنت متأكد من حذف هذا الفصل؟ \n\nسيتم إلغاء تسجيل جميع الطلاب من هذا الفصل. لا يمكن التراجع عن هذا الإجراء.",
        "assign_students_to": "تعيين طلاب إلى {class_name}",
        "select_students_to_assign": "اختر الطلاب لتعيينهم إلى هذا الفصل:",
        "save": "حفظ",
        "take_attendance": "أخذ الحضور: {class_name}",
        "no_subjects_found_for_class": "لم يتم العثور على مواد لهذا الفصل. يرجى إضافة مواد أولاً.",
        "go_to_subjects": "الذهاب إلى المواد",
        "select_subject": "اختر المادة",
        "choose_a_subject": "اختر مادة",
        "select_subject_for_attendance": "اختر مادة لأخذ الحضور لها:",
        "get_attendance": "أخذ الحضور",
        "please_enter_class_name": "يرجى إدخال اسم الفصل",
        "failed_to_rename_class": "فشل في إعادة تسمية الفصل",
        "failed_to_delete_class": "فشل في حذف الفصل",
        "please_select_subject": "يرجى اختيار مادة",
        "failed_to_start_video_stream": "فشل في بدء تشغيل الفيديو",

        # Subjects page
        "unknown_class": "فصل غير معروف",
        "your_subjects": "موادك ({count})",
        "edit_subject": "تعديل المادة",
        "delete_subject": "حذف المادة",
        "teacher": "المعلم",
        "no_teacher_assigned": "لا يوجد معلم مُعيَّن",
        "no_subjects_found": "لم يتم العثور على مواد",
        "no_subjects_match_filters": "لا توجد مواد تطابق المرشحات المحددة",
        "no_subjects_found_for_query": "لم يتم العثور على مواد لـ '{query}'",
        "search_subjects": "البحث في المواد...",
        "filter_by_class": "تصفية حسب الفصل",
        "all_classes": "جميع الفصول",
        "add_subject": "إضافة مادة",
        "subject_name": "اسم المادة",
        "teacher_name": "اسم المعلم",
        "class_assignment": "تعيين الفصل",
        "select_class": "اختر فصلاً",
        "enter_subject_name": "أدخل اسم المادة",
        "enter_teacher_name": "أدخل اسم المعلم (اختياري)",
        "please_enter_subject_name": "يرجى إدخال اسم المادة",
        "please_select_class": "يرجى اختيار فصل",
        "failed_to_add_subject": "فشل في إضافة المادة",
        "edit_subject_title": "تعديل المادة: {subject_name}",
        "failed_to_update_subject": "فشل في تحديث المادة",
        "delete_subject_title": "حذف المادة: {subject_name}",
        "are_you_sure_delete_subject": "هل أنت متأكد من حذف هذه المادة؟\n\nلا يمكن التراجع عن هذا الإجراء.",
        "failed_to_delete_subject": "فشل في حذف المادة",
        "filter_subjects": "تصفية المواد",
        "add_new_subject": "إضافة مادة جديدة",
        "try_adjusting_filters": "جرب تعديل المرشحات أو إنشاء مادة جديدة",
        "failed_to_create_subject": "فشل في إنشاء المادة",
        "edit_subject_details": "تعديل تفاصيل المادة:",
        "subject_updated_successfully": "تم تحديث المادة بنجاح",
        "delete_subject_confirmation": "هل أنت متأكد من حذف هذه المادة؟\n\nلا يمكن التراجع عن هذا الإجراء.",

        # Quizzes page
        "filter_quizzes": "تصفية الاختبارات",
        "all_subjects": "جميع المواد",
        "select_subject": "اختر مادة",
        "filter_by_subject": "تصفية حسب المادة",

        # Student details page
        "student_details": "تفاصيل الطالب",
        "back_to_classes": "العودة إلى الفصول",
        "face_enrolled": "الوجه مسجل",
        "face_not_enrolled": "الوجه غير مسجل",
        "class": "الفصل",
        "completed": "مكتمل",
        "recent_activity": "النشاط الأخير",
        "no_recent_activity": "لا يوجد نشاط حديث",
        "attendance_by_subject": "الحضور حسب المادة",
        "view_full_history": "عرض التاريخ الكامل"
    },

    LANGUAGE_FRENCH: {
        # App general
        "app_name": "Assistant Enseignant",
        "dashboard": "Tableau de Bord",
        "classes": "Classes",
        "students": "Étudiants",
        "subjects": "Matières",
        "quizzes": "Quiz",
        "attendance": "Présences",
        "settings": "Paramètres",

        # Authentication
        "login": "Connexion",
        "username": "Nom d'utilisateur",
        "password": "Mot de passe",
        "username_required": "Le nom d'utilisateur est requis",
        "password_required": "Le mot de passe est requis",
        "invalid_username": "Nom d'utilisateur invalide",
        "invalid_password": "Mot de passe invalide",
        "please_login_to_continue": "Veuillez vous connecter pour continuer",

        # Settings
        "language": "Langue",
        "select_language": "Sélectionner la Langue",
        "theme": "Thème",
        "light_mode": "Mode Clair",
        "dark_mode": "Mode Sombre",
        "change_credentials": "Modifier les Identifiants",
        "change_username": "Modifier le Nom d'Utilisateur",
        "change_password": "Modifier le Mot de Passe",
        "current_password": "Mot de Passe Actuel",
        "new_username": "Nouveau Nom d'Utilisateur",
        "new_password": "Nouveau Mot de Passe",
        "confirm_password": "Confirmer le Mot de Passe",
        "invalid_current_password": "Mot de passe actuel invalide",
        "username_required": "Nom d'utilisateur requis",
        "password_required": "Mot de passe requis",
        "username_too_short": "Le nom d'utilisateur doit contenir au moins 3 caractères",
        "password_too_short": "Le mot de passe doit contenir au moins 6 caractères",
        "passwords_dont_match": "Les mots de passe ne correspondent pas",
        "credentials_updated": "Identifiants mis à jour avec succès",
        "username_updated": "Nom d'utilisateur mis à jour avec succès",
        "password_updated": "Mot de passe mis à jour avec succès",
        "save": "Enregistrer",
        "save_username": "Enregistrer le Nom d'Utilisateur",
        "save_password": "Enregistrer le Mot de Passe",
        "cancel": "Annuler",
        "reset": "Réinitialiser",
        "settings_saved": "Paramètres enregistrés avec succès",
        "settings_description": "Gérez vos préférences et paramètres de compte",
        "current_account": "Compte Actuel",

        # Dashboard
        "view_details": "Voir les Détails",

        # Common actions
        "add": "Ajouter",
        "edit": "Modifier",
        "delete": "Supprimer",
        "close": "Fermer",
        "back": "Retour",
        "search": "Rechercher",
        "no_data": "Pas de Données",

        # Students page
        "student_management": "Gestion des Étudiants",
        "enrolled_students": "Étudiants Inscrits",
        "filter_by_class": "Filtrer par Classe",
        "select_class": "Sélectionner une classe",
        "add_new_student": "Ajouter un Nouvel Étudiant",
        "add_student": "Ajouter un Étudiant",
        "showing_all": "Affichage de tous les",
        "showing": "Affichage de",
        "found": "Trouvé",
        "students_matching": "étudiants correspondant à",
        "in": "dans",
        "clear_search": "Effacer la recherche",
        "student": "Étudiant",
        "deleted": "supprimé",
        "failed_to_delete_student": "Échec de la suppression de l'étudiant",
        "delete_student": "Supprimer l'Étudiant",
        "delete_student_confirmation": "Êtes-vous sûr de vouloir supprimer cet étudiant? Cette action ne peut pas être annulée.",
        "student_name": "Nom de l'Étudiant",
        "enter_student_name": "Entrez le nom de l'étudiant",
        "please_enter_student_name": "Veuillez entrer le nom de l'étudiant",
        "face_enrollment_future": "La fonctionnalité d'inscription faciale sera implémentée dans une future mise à jour.",
        "would_be_added": "serait ajouté ici",
        "enter_student_name_prompt": "Entrez le nom de l'étudiant:",
        "face_enrollment_note": "Remarque: Dans une implémentation réelle, cela capturerait des images faciales pour l'inscription.",
        "update_face_encoding_for": "Mettre à jour l'encodage facial pour",
        "upload_clear_face_image": "Veuillez télécharger au moins une image claire du visage de l'étudiant",
        "captured_images": "Images Capturées",
        "update_face_encoding": "Mettre à jour l'Encodage Facial",
        "update": "Mettre à jour",
        "upload_image": "Télécharger une Image",
        "max_images_allowed": "Maximum de 5 images autorisées. Veuillez supprimer certaines images d'abord.",
        "only_add": "Vous ne pouvez ajouter que",
        "more_images": "image(s) supplémentaire(s). Veuillez sélectionner moins d'images.",
        "error_processing_image": "Erreur lors du traitement de l'image",
        "no_face_detected": "Aucun visage détecté dans l'image. Veuillez réessayer.",
        "multiple_faces_detected": "Plusieurs visages détectés dans l'image. Veuillez utiliser une image avec un seul visage.",
        "please_upload_image": "Veuillez télécharger au moins une image",
        "processing": "Traitement en cours",
        "updating_face_encoding": "Mise à jour de l'encodage facial...",
        "face_encoding_for": "Encodage facial pour",
        "updated_successfully": "mis à jour avec succès",
        "failed_to_update": "Échec de la mise à jour pour",
        "error_updating": "Erreur lors de la mise à jour",

        # Classes page
        "class_management": "Gestion des Classes",
        "class_name": "Nom de la Classe",
        "enter_class_name": "Entrez le nom de la nouvelle classe",
        "description_optional": "Description (Optionnelle)",
        "enter_class_description": "Entrez la description de la classe",
        "please_enter_class_name": "Veuillez entrer un nom de classe",
        "failed_to_create_class": "Échec de la création de la classe",
        "add_class": "Ajouter une Classe",
        "add_new_class": "Ajouter une Nouvelle Classe",
        "no_classes_yet": "Pas encore de classes",
        "create_first_class": "Créez votre première classe en utilisant le formulaire ci-dessus",
        "existing_classes": "Classes Existantes",
        "view_students": "Voir les Étudiants",
        "rename_class": "Renommer la Classe",
        "delete_class": "Supprimer la Classe",
        "assign_students": "Assigner des Étudiants",
        "take_attendance": "Prendre les Présences",
        "qr_enrollment": "Inscription par QR Code",
        "student": "étudiant",
        "students": "étudiants",

        # Subjects page
        "subject_management": "Gestion des Matières",
        "subject_name": "Nom de la Matière",
        "enter_subject_name": "Entrez le nom de la matière",
        "teacher_name": "Nom de l'Enseignant",
        "teacher_name_optional": "Nom de l'Enseignant (Optionnel)",
        "enter_teacher_name": "Entrez le nom de l'enseignant",
        "please_select_class": "Veuillez sélectionner une classe",
        "please_enter_subject_name": "Veuillez entrer un nom de matière",
        "subject": "Matière",
        "created_successfully": "créée avec succès",
        "failed_to_create_subject": "Échec de la création de la matière",
        "add_subject": "Ajouter une Matière",
        "add_new_subject": "Ajouter une Nouvelle Matière",
        "existing_subjects": "Matières Existantes",
        "edit_subject": "Modifier la Matière",
        "edit_subject_details": "Modifier les détails de la matière:",
        "subject_updated_successfully": "Matière mise à jour avec succès",
        "failed_to_update_subject": "Échec de la mise à jour de la matière",
        "delete_subject": "Supprimer la Matière",
        "delete_subject_confirmation": "Êtes-vous sûr de vouloir supprimer cette matière? Cette action ne peut pas être annulée.",
        "failed_to_delete_subject": "Échec de la suppression de la matière",
        "choose_class": "Choisissez une classe",
        "select_class": "Sélectionner une Classe",

        # Quizzes page
        "quiz_management": "Gestion des Quiz",
        "quiz_title": "Titre du Quiz",
        "enter_quiz_title": "Entrez le titre du quiz",
        "quiz_description": "Description du Quiz",
        "enter_quiz_description": "Entrez la description du quiz",
        "select_subject": "Sélectionner une Matière",
        "add_quiz": "Ajouter un Quiz",
        "create_quiz": "Créer un Quiz",
        "no_quizzes_yet": "Pas encore de quiz",
        "create_first_quiz": "Créez votre premier quiz en utilisant le formulaire ci-dessus",
        "existing_quizzes": "Quiz Existants",
        "view_quiz_details": "Voir les Détails du Quiz",
        "edit_quiz": "Modifier le Quiz",
        "delete_quiz": "Supprimer le Quiz",
        "question_text": "Texte de la Question",
        "enter_question": "Entrez la question",
        "add_question": "Ajouter une Question",
        "option_text": "Texte de l'Option",
        "enter_option": "Entrez l'option",
        "is_correct": "Est la Bonne Réponse",
        "add_option": "Ajouter une Option",
        "save_quiz": "Enregistrer le Quiz",
        "cancel_quiz": "Annuler",
        "please_enter_quiz_title": "Veuillez entrer un titre de quiz",
        "failed_to_create_quiz": "Échec de la création du quiz",

        # Attendance page
        "attendance_management": "Gestion des Présences",
        "attendance_history": "Historique des Présences",
        "filter_attendance": "Filtrer les Présences",
        "date_range": "Plage de Dates",
        "from_date": "Date de Début",
        "to_date": "Date de Fin",
        "select_date": "Sélectionner la Date",
        "all_classes": "Toutes les Classes",
        "all_subjects": "Toutes les Matières",
        "all_students": "Tous les Étudiants",
        "present": "Présent",
        "absent": "Absent",
        "status": "Statut",
        "date": "Date",
        "time": "Heure",
        "total_records": "Total des Enregistrements",
        "attendance_rate": "Taux de Présence",
        "export_data": "Exporter les Données",
        "no_attendance_records": "Aucun enregistrement de présence trouvé",
        "attendance_statistics": "Statistiques de Présence",
        "total_days": "Total des Jours",
        "clear_filters": "Effacer les Filtres",
        "apply_filters": "Appliquer les Filtres",

        # Start page
        "ministry_education_tunisia": "Ministère de l'Éducation Tunisie",
        "educational_management_system": "Système de gestion éducative pour enseignants",
        "get_started": "Commencer",

        # Navigation
        "overview": "Aperçu",
        "overview_stats": "Aperçu et Statistiques",
        "classes_count": "classes",
        "students_count": "étudiants",
        "subjects_available": "matières disponibles",
        "quizzes_created": "quiz créés",

        # Tooltips
        "menu": "Menu",
        "settings_tooltip": "Paramètres",

        # Error messages and dialogs
        "database_error": "Erreur de Base de Données",
        "database_error_message": "Erreur de base de données. Veuillez redémarrer l'application.",
        "retry": "Réessayer",
        "confirm": "Confirmer",
        "submit": "Soumettre",

        # Dashboard
        "welcome": "Bienvenue",

        # Console and debug messages
        "settings_button_clicked": "Bouton paramètres cliqué",
        "opening_settings_dialog": "Ouverture de la boîte de dialogue des paramètres...",
        "qr_display_initialized": "Affichage QR initialisé en mode {mode} ({width}x{height})",
        "mode_failed": "Le mode {mode} a échoué",
        "all_display_modes_failed": "Tous les modes d'affichage ont échoué",
        "could_not_set_mouse_cursor": "Impossible de définir la visibilité du curseur de la souris",
        "failed_to_initialize_pygame": "Échec de l'initialisation de pygame pour l'affichage QR",
        "failed_to_generate_qr_code": "Échec de la génération du code QR",
        "font_rendering_failed": "Le rendu de la police a échoué",
        "switching_to_face_mode": "Passage au mode visage dans la même fenêtre...",
        "switching_to_enrollment_qr": "Passage au mode QR d'inscription pour la classe: {class_name}",
        "switching_back_to_face_from_enrollment": "Retour à l'affichage du visage depuis l'inscription...",
        "switching_to_quiz_qr": "Passage au mode QR de quiz pour le quiz: {quiz_title}",
        "switching_back_to_face_from_quiz": "Retour à l'affichage du visage depuis le quiz...",

        # QR Display buttons and captions
        "teacher_assistant_qr_code": "Assistant Enseignant - Code QR",
        "show_face": "Afficher le Visage",
        "show_qr_code": "Afficher le Code QR",
        "wifi_settings": "Paramètres WiFi",
        "close_enrollment": "Fermer l'Inscription",
        "close_quiz": "Fermer le Quiz",
        "enrollment_title": "Inscription: {class_name}",
        "students_can_scan_enrollment": "Les étudiants peuvent scanner ce code QR pour s'inscrire",
        "quiz_title_display": "Quiz: {quiz_title}",
        "students_can_scan_quiz": "Les étudiants peuvent scanner ce code QR pour passer le quiz",

        # Video streaming
        "initializing_jetson_camera": "Initialisation de la caméra Jetson...",
        "camera_object_created": "Objet caméra créé avec succès",
        "initializing_focuser": "Initialisation du focaliseur sur le bus I2C {bus}...",
        "focuser_initialized": "Focaliseur initialisé avec succès",
        "setting_focus": "Réglage de la mise au point à {value}...",
        "jetson_camera_initialized": "Caméra Jetson initialisée avec succès",
        "focus_set": "Mise au point réglée à {value} sur le bus I2C {bus}",
        "failed_to_initialize_camera": "Échec de l'initialisation de la caméra Jetson",
        "error_details": "Détails de l'erreur",
        "attendance_video_stream": "Flux Vidéo de Présence",
        "live_attendance_stream": "Flux de Présence en Direct",
        "class_label": "Classe:",
        "subject_label": "Matière:",
        "students_recognized": "Étudiants Reconnus:",
        "take_attendance": "Prendre les Présences",
        "save_attendance": "Sauvegarder les Présences",
        "stop_stream": "Arrêter le Flux",
        "student_attendance": "Présence des Étudiants",
        "no_students_enrolled": "Aucun étudiant inscrit dans cette classe.",
        "mark_absent": "Marquer Absent",
        "mark_present": "Marquer Présent",
        "present": "Présent",
        "absent": "Absent",
        "recognized": "Reconnu",
        "taking": "En cours...",
        "saving": "Sauvegarde...",
        "attendance_captured_successfully": "Présences Capturées avec Succès!",
        "video_recording_stopped": "Enregistrement vidéo arrêté. Vérifiez les présences et sauvegardez quand vous êtes prêt.",
        "attendance_saved_successfully": "Présences sauvegardées avec succès! La fenêtre va se fermer.",
        "error_taking_attendance": "Erreur lors de la prise des présences",
        "error_saving_attendance": "Erreur lors de la sauvegarde des présences",
        "error_updating_attendance": "Erreur lors de la mise à jour des présences",
        "attendance_captured": "Présences capturées! {count} étudiants reconnus.",
        "error_starting_stream": "Erreur lors du démarrage du flux",

        # Quiz management
        "add": "Ajouter",
        "create_new_quiz": "Créer un Nouveau Quiz",
        "please_enter_quiz_title": "Veuillez saisir un titre de quiz",
        "please_select_class": "Veuillez sélectionner une classe",
        "please_select_subject": "Veuillez sélectionner une matière",
        "error_creating_quiz": "Erreur lors de la création du quiz",
        "quizzes_count": "{count} quiz créés",
        "your_quizzes": "Vos Quiz ({count}){filter}",
        "unknown_class": "Classe Inconnue",
        "unknown_subject": "Matière Inconnue",
        "quizzes_word": "quiz",
        "qr_code": "Code QR",
        "results": "Résultats",
        "questions_count": "{count} questions",
        "no_quizzes_found": "Aucun quiz trouvé",
        "no_quizzes_match_filters": "Aucun quiz ne correspond aux filtres sélectionnés",
        "try_adjusting_filters": "Essayez d'ajuster vos filtres ou de créer un nouveau quiz",
        "quiz_not_found": "Quiz non trouvé",
        "enter_option_text": "Saisir le texte de l'option...",
        "remove_option": "Supprimer l'option",
        "please_enter_question": "Veuillez saisir une question",
        "please_enter_option_text": "Veuillez saisir le texte de l'option",
        "please_enter_at_least_two_options": "Veuillez saisir au moins deux options",
        "please_mark_at_least_one_correct": "Veuillez marquer au moins une option comme correcte",
        "question_added_successfully": "Question ajoutée avec succès",
        "failed_to_add_question": "Échec de l'ajout de la question",
        "question_deleted_successfully": "Question supprimée avec succès",
        "failed_to_delete_question": "Échec de la suppression de la question",
        "add_new_question": "Ajouter une Nouvelle Question",
        "enter_your_question_here": "Saisissez votre question ici...",
        "answer_options": "Options de Réponse",
        "add_option": "Ajouter une Option",
        "cancel": "Annuler",
        "done": "Terminé",
        "select_class": "Sélectionner une classe",
        "select_subject": "Sélectionner une matière",
        "please_enter_description": "Veuillez saisir une description",
        "quiz_details_updated_successfully": "Détails du quiz mis à jour avec succès",
        "failed_to_update_quiz_details": "Échec de la mise à jour des détails du quiz",
        "quiz_title": "Titre du quiz",
        "description": "Description",
        "questions_label": "Questions ({count})",
        "ready_to_create_questions": "Prêt à Créer des Questions?",
        "add_engaging_questions": "Ajoutez des questions engageantes pour rendre votre quiz interactif!",
        "create_your_first_question": "Créer Votre Première Question",
        "add_question": "Ajouter une Question",
        "save": "Sauvegarder",
        "quiz_active": "Quiz Actif",
        "qr_code_showing_on_robot": "Le code QR pour '{title}' s'affiche maintenant sur l'écran du robot.",
        "students_can_scan_to_take_quiz": "Les étudiants peuvent scanner le code QR pour passer ce quiz.",
        "failed_to_display_qr_code": "Échec de l'affichage du code QR sur l'écran du robot",
        "quiz_qr_code_displayed": "Code QR du quiz affiché sur l'écran du visage pour le quiz: {title}",
        "or_share_this_link": "Ou partagez ce lien:",
        "close_quiz": "Fermer le Quiz",
        "quiz_active_title": "Quiz Actif - {title}",
        "quiz_closed_message": "Quiz '{title}' a été fermé. L'écran du robot est revenu à l'affichage du visage.",
        "quiz_dialog_dismissed": "Boîte de dialogue du quiz fermée",

        # Quiz results
        "result_deleted": "résultat de {name} supprimé",
        "failed_to_delete_result": "Échec de la suppression du résultat",
        "no_quiz_results_yet": "Aucun Résultat de Quiz Encore",
        "students_havent_submitted": "Les étudiants n'ont pas encore soumis de réponses pour ce quiz.",
        "quiz_results_title": "Résultats du Quiz - {title}",
        "total_submissions": "Total des Soumissions: {count}",
        "close": "Fermer",
        "view_answers": "Voir les Réponses",
        "delete_result": "Supprimer le Résultat",
        "view_detailed_answers": "Voir les réponses détaillées",
        "delete_result_tooltip": "Supprimer le résultat",
        "score_display": "Score: {score}/{total} ({percentage}%)",
        "date_display": "Date: {date}",
        "could_not_load_student_answers": "Impossible de charger les réponses de l'étudiant",
        "student_answers_title": "Réponses de {name} - {title}",
        "final_score": "Score Final: {score}/{total} ({percentage}%)",
        "back_to_results": "Retour aux Résultats",
        "student_answer_label": "Réponse de l'Étudiant:",
        "correct_answer_label": "Réponse Correcte:",
        "no_answer": "Aucune réponse",
        "are_you_sure_delete_quiz": "Êtes-vous sûr de vouloir supprimer '{title}'? Cette action ne peut pas être annulée.",

        # Classes page additional translations
        "select_students_to_assign": "Sélectionnez les étudiants à assigner à cette classe:",
        "assign_students_to": "Assigner des Étudiants à {class_name}",
        "save": "Sauvegarder",
        "take_attendance_title": "Prendre les Présences: {class_name}",
        "no_subjects_found": "Aucune matière trouvée pour cette classe. Veuillez d'abord ajouter des matières.",
        "go_to_subjects": "Aller aux Matières",
        "select_subject": "Sélectionner une Matière",
        "choose_subject": "Choisir une matière",
        "please_select_subject": "Veuillez sélectionner une matière",
        "select_subject_for_attendance": "Sélectionnez une matière pour prendre les présences:",
        "get_attendance": "Prendre les Présences",
        "failed_to_start_video_stream": "Échec du démarrage du flux vidéo",
        "enrollment_active": "Inscription Active",
        "qr_code_for_class_showing": "Le code QR pour '{class_name}' s'affiche maintenant sur l'écran du robot.",
        "students_can_scan_enrollment": "Les étudiants peuvent scanner ce code QR pour s'inscrire",
        "failed_to_display_enrollment_qr": "Échec de l'affichage du code QR sur l'écran du robot",
        "enrollment_qr_displayed": "Code QR d'inscription affiché sur l'écran du visage pour la classe: {class_name}",
        "close_enrollment": "Fermer l'Inscription",
        "enrollment_active_title": "Inscription Active - {class_name}",
        "enrollment_closed_message": "Inscription pour '{class_name}' fermée. L'écran du robot est revenu à l'affichage du visage.",
        "enrollment_dialog_dismissed": "Boîte de dialogue d'inscription fermée",
        "attendance_history_title": "Historique des Présences - {class_name}",
        "no_attendance_records": "Aucun Enregistrement de Présence",
        "no_attendance_taken": "Aucune présence n'a été prise pour cette classe.",
        "start_taking_attendance": "Commencez à prendre les présences pour voir l'historique ici.",

        # Classes page
        "search_classes": "Rechercher des classes...",
        "classes_available": "classes disponibles",
        "of_classes_found": "sur {total} classes trouvées",
        "no_classes_found": "Aucune classe trouvée pour '{query}'",
        "try_different_search": "Essayez un terme de recherche différent",
        "create_first_class": "Créez votre première classe pour commencer",
        "enter_class_description": "Entrez la description de la classe (optionnel)",
        "add_new_class": "Ajouter une Nouvelle Classe",
        "search_students": "Rechercher des étudiants...",
        "no_students_found": "Aucun étudiant trouvé pour '{query}'",
        "no_students_in_class": "Aucun étudiant trouvé dans la classe '{class_name}'",
        "add_students_to_class": "Ajoutez des étudiants à cette classe pour les voir ici",
        "unknown": "Inconnu",
        "enrolled": "Inscrit",
        "sessions": "Séances",
        "attendance_rate": "Taux de Présence",
        "present": "Présent",

        # Dialog titles and messages
        "class_students": "Étudiants de la Classe",
        "students_in": "Étudiants dans {class_name}",
        "no_students_in_this_class": "Aucun étudiant dans cette classe",
        "avg_attendance": "présence moyenne",
        "new_class_name": "Nouveau Nom de Classe",
        "enter_new_name_for_class": "Entrez un nouveau nom pour cette classe :",
        "rename_class": "Renommer la Classe : {class_name}",
        "rename": "Renommer",
        "delete_class": "Supprimer la Classe : {class_name}",
        "are_you_sure_delete_class": "Êtes-vous sûr de vouloir supprimer cette classe ? \n\nTous les étudiants seront désassignés de cette classe. Cette action ne peut pas être annulée.",
        "assign_students_to": "Assigner des Étudiants à {class_name}",
        "select_students_to_assign": "Sélectionnez les étudiants à assigner à cette classe :",
        "save": "Enregistrer",
        "take_attendance": "Prendre les Présences : {class_name}",
        "no_subjects_found_for_class": "Aucune matière trouvée pour cette classe. Veuillez d'abord ajouter des matières.",
        "go_to_subjects": "Aller aux Matières",
        "select_subject": "Sélectionner une Matière",
        "choose_a_subject": "Choisissez une matière",
        "select_subject_for_attendance": "Sélectionnez une matière pour prendre les présences :",
        "get_attendance": "Prendre les Présences",
        "please_enter_class_name": "Veuillez entrer un nom de classe",
        "failed_to_rename_class": "Échec du renommage de la classe",
        "failed_to_delete_class": "Échec de la suppression de la classe",
        "please_select_subject": "Veuillez sélectionner une matière",
        "failed_to_start_video_stream": "Échec du démarrage du flux vidéo",

        # Subjects page
        "unknown_class": "Classe Inconnue",
        "your_subjects": "Vos Matières ({count})",
        "edit_subject": "Modifier la Matière",
        "delete_subject": "Supprimer la Matière",
        "teacher": "Enseignant",
        "no_teacher_assigned": "Aucun enseignant assigné",
        "no_subjects_found": "Aucune matière trouvée",
        "no_subjects_match_filters": "Aucune matière ne correspond aux filtres sélectionnés",
        "no_subjects_found_for_query": "Aucune matière trouvée pour '{query}'",
        "search_subjects": "Rechercher des matières...",
        "filter_by_class": "Filtrer par classe",
        "all_classes": "Toutes les classes",
        "add_subject": "Ajouter une Matière",
        "subject_name": "Nom de la Matière",
        "teacher_name": "Nom de l'Enseignant",
        "class_assignment": "Assignation de Classe",
        "select_class": "Sélectionner une classe",
        "enter_subject_name": "Entrez le nom de la matière",
        "enter_teacher_name": "Entrez le nom de l'enseignant (optionnel)",
        "please_enter_subject_name": "Veuillez entrer un nom de matière",
        "please_select_class": "Veuillez sélectionner une classe",
        "failed_to_add_subject": "Échec de l'ajout de la matière",
        "edit_subject_title": "Modifier la Matière : {subject_name}",
        "failed_to_update_subject": "Échec de la mise à jour de la matière",
        "delete_subject_title": "Supprimer la Matière : {subject_name}",
        "are_you_sure_delete_subject": "Êtes-vous sûr de vouloir supprimer cette matière ?\n\nCette action ne peut pas être annulée.",
        "failed_to_delete_subject": "Échec de la suppression de la matière",
        "filter_subjects": "Filtrer les Matières",
        "add_new_subject": "Ajouter une Nouvelle Matière",
        "try_adjusting_filters": "Essayez d'ajuster vos filtres ou de créer une nouvelle matière",
        "failed_to_create_subject": "Échec de la création de la matière",
        "edit_subject_details": "Modifier les détails de la matière :",
        "subject_updated_successfully": "Matière mise à jour avec succès",
        "delete_subject_confirmation": "Êtes-vous sûr de vouloir supprimer cette matière ?\n\nCette action ne peut pas être annulée.",

        # Quizzes page
        "filter_quizzes": "Filtrer les Quiz",
        "all_subjects": "Toutes les Matières",
        "select_subject": "Sélectionner une Matière",
        "filter_by_subject": "Filtrer par Matière",

        # WiFi and network additional translations
        "hidden_network": "Réseau Masqué",
        "open_network": "Ouvert",
        "secured_network": "Sécurisé",
        "unknown_network": "Inconnu",
        "wpa2_network": "WPA2",
        "wpa_network": "WPA",

        # Console and debug messages
        "trying_iwlist_fallback": "Tentative avec iwlist en secours...",
        "iwlist_scan_failed": "Échec du scan iwlist",
        "wifi_scan_timed_out": "Délai d'attente du scan WiFi dépassé",
        "error_scanning_wifi": "Erreur lors du scan des réseaux WiFi",
        "nmcli_scan_failed": "Échec du scan nmcli",
        "nmcli_connection_failed": "Échec de la connexion nmcli",
        "successfully_connected_to": "Connecté avec succès à",
        "networkmanager_not_available": "NetworkManager non disponible, tentative avec wpa_supplicant...",
        "error_connecting_to_wifi": "Erreur lors de la connexion WiFi",
        "no_wifi_interface_found": "Aucune interface WiFi trouvée",
        "method_requires_admin": "Cette méthode nécessite des privilèges administrateur...",
        "failed_to_start_wpa_supplicant": "Échec du démarrage de wpa_supplicant",
        "failed_to_get_ip_address": "Échec de l'obtention de l'adresse IP",
        "failed_to_connect_to": "Échec de la connexion à",
        "error_getting_wifi_interface": "Erreur lors de l'obtention de l'interface WiFi",
        "restarting_application": "🔄 Redémarrage de l'application...",

        # Face processing and enrollment
        "no_valid_face_images": "Aucune image de visage valide n'a été téléchargée. Veuillez réessayer avec des photos claires de visages.",
        "image_error_saving": "Erreur lors de la sauvegarde de l'image",

        # Additional card translations
        "class": "Classe",
        "not_assigned_to_class": "Non assigné à une classe",
        "edit_student": "Modifier l'Étudiant",
        "delete_student": "Supprimer l'Étudiant",

        # Additional subjects page translations
        "of_subjects_found": "sur {total} matières trouvées",
        "searching": "recherche",
        "no_subjects_yet": "Aucune matière encore",
        "subject": "Matière",
        "created_successfully": "créée avec succès",
        "deleted": "supprimée",

        # Student count translations
        "no_students": "Aucun étudiant",
        "student_singular": "étudiant",
        "students_plural": "étudiants",

        # Simplified menu items (without class names)
        "take_attendance_simple": "Prendre les Présences",
        "rename_class_simple": "Renommer",
        "delete_class_simple": "Supprimer",

        # Student details page
        "student_details": "Détails de l'Étudiant",
        "back_to_classes": "Retour aux Classes",
        "face_enrolled": "Visage Inscrit",
        "face_not_enrolled": "Visage Non Inscrit",
        "completed": "Terminé",
        "recent_activity": "Activité Récente",
        "no_recent_activity": "Aucune activité récente",
        "attendance_by_subject": "Présence par Matière",
        "view_full_history": "Voir l'Historique Complet"
    }
}

def get_text(key, language=DEFAULT_LANGUAGE):
    """
    Get translated text for a key.

    Args:
        key: The translation key
        language: The language code (default: DEFAULT_LANGUAGE)

    Returns:
        str: The translated text or the key if translation not found
    """
    # If language not available, fall back to default
    if language not in TRANSLATIONS:
        language = DEFAULT_LANGUAGE

    # Get translation or fall back to key
    return TRANSLATIONS.get(language, {}).get(key, key)

def create_language_dropdown(page: ft.Page, on_change=None):
    """
    Create a language selection dropdown.

    Args:
        page: The Flet page object
        on_change: Function to call when language changes

    Returns:
        ft.Dropdown: The language dropdown
    """
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    dropdown = ft.Dropdown(
        label=get_text("select_language", current_language),
        value=current_language,
        options=[
            ft.dropdown.Option(
                key=lang_code,
                text=lang_name
            ) for lang_code, lang_name in LANGUAGE_NAMES.items()
        ],
        width=200,
        on_change=on_change
    )

    return dropdown
